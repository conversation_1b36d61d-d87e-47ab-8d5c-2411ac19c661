﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationManifest>Data\SeedData\ProductSeed.cs</ApplicationManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.18" />
    <PackageReference Include="Hangfire.Core" Version="1.8.18" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.18" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Facebook" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10" />
    <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
    <PackageReference Include="Stripe.net" Version="47.4.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="7.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Croppilot.Date\Croppilot.Date.csproj" />
  </ItemGroup>

</Project>
