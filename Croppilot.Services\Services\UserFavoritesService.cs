using Croppilot.Date.Models;
using Croppilot.Infrastructure.Repositories.Interfaces;
using Croppilot.Services.Abstract;
using Microsoft.Extensions.Logging;

namespace Croppilot.Services.Services;

public class UserFavoritesService : IUserFavoritesService
{
    private readonly ICacheService _cacheService;
    private readonly ICacheKeyGenerator _cacheKeyGenerator;
    private readonly IWishlistService _wishlistService;
    private readonly ILogger<UserFavoritesService> _logger;
    
    private const int CACHE_EXPIRATION_MINUTES = 30;

    public UserFavoritesService(
        ICacheService cacheService,
        ICacheKeyGenerator cacheKeyGenerator,
        IWishlistService wishlistService,
        ILogger<UserFavoritesService> logger)
    {
        _cacheService = cacheService;
        _cacheKeyGenerator = cacheKeyGenerator;
        _wishlistService = wishlistService;
        _logger = logger;
    }

    public async Task<Dictionary<int, bool>> GetUserFavoritesAsync(string userId, List<int> productIds, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(userId) || !productIds.Any())
        {
            return productIds.ToDictionary(id => id, _ => false);
        }

        try
        {
            var cacheKey = _cacheKeyGenerator.GenerateUserKey(userId, "favorites");
            var cachedFavorites = await _cacheService.GetAsync<UserProductFavorites>(cacheKey, cancellationToken);

            if (cachedFavorites != null && IsValidCache(cachedFavorites))
            {
                // Return cached favorites for requested products
                return productIds.ToDictionary(
                    id => id, 
                    id => cachedFavorites.FavoriteStatuses.GetValueOrDefault(id, false)
                );
            }

            // Cache miss or expired - refresh from database
            return await RefreshAndGetUserFavoritesAsync(userId, productIds, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user favorites for user {UserId}", userId);
            return productIds.ToDictionary(id => id, _ => false);
        }
    }

    public async Task<bool> GetUserFavoriteAsync(string userId, int productId, CancellationToken cancellationToken = default)
    {
        var favorites = await GetUserFavoritesAsync(userId, [productId], cancellationToken);
        return favorites.GetValueOrDefault(productId, false);
    }

    public async Task RefreshUserFavoritesAsync(string userId, List<int> productIds, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(userId))
            return;

        try
        {
            await RefreshAndGetUserFavoritesAsync(userId, productIds, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing user favorites for user {UserId}", userId);
        }
    }

    public async Task InvalidateUserFavoritesAsync(string userId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(userId))
            return;

        try
        {
            var cacheKey = _cacheKeyGenerator.GenerateUserKey(userId, "favorites");
            await _cacheService.RemoveAsync(cacheKey, cancellationToken);
            _logger.LogDebug("Invalidated user favorites cache for user {UserId}", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating user favorites for user {UserId}", userId);
        }
    }

    private async Task<Dictionary<int, bool>> RefreshAndGetUserFavoritesAsync(string userId, List<int> productIds, CancellationToken cancellationToken)
    {
        // Get user's wishlist from database
        var wishlist = await _wishlistService.GetWishlistByUserIdAsync(userId);
        
        // Create favorites mapping for all requested products
        var favoriteStatuses = productIds.ToDictionary(
            id => id,
            id => wishlist?.WishlistItems?.Any(wi => wi.ProductId == id) == true
        );

        // Cache the favorites
        var userFavorites = new UserProductFavorites(
            userId,
            favoriteStatuses,
            DateTime.UtcNow
        );

        var cacheKey = _cacheKeyGenerator.GenerateUserKey(userId, "favorites");
        var expiration = TimeSpan.FromMinutes(CACHE_EXPIRATION_MINUTES);
        await _cacheService.SetAsync(cacheKey, userFavorites, expiration, cancellationToken);

        _logger.LogDebug("Refreshed user favorites cache for user {UserId} with {Count} products", 
            userId, productIds.Count);

        return favoriteStatuses;
    }

    private static bool IsValidCache(UserProductFavorites cachedFavorites)
    {
        // Cache is valid for 30 minutes
        return DateTime.UtcNow - cachedFavorites.LastUpdated < TimeSpan.FromMinutes(CACHE_EXPIRATION_MINUTES);
    }
} 