{
  "ConnectionStrings": {
    //"Default": "Server=db9040.public.databaseasp.net; Database=db9040; User Id=db9040; Password=************; Encrypt=False; MultipleActiveResultSets=True;",
    "Default": "Data Source=db-server-zonkol.database.windows.net,1433;Initial Catalog=crop-guard-database;User ID=db-server-zonkol-admin;Password=************",
    "WatchDog": "Server=tcp:crop-guard.database.windows.net,1433;Initial Catalog=Monitoring;Persist Security Info=False;User ID=crop_admin;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    "HangfireConnection": "Server=db14840.public.databaseasp.net; Database=db14840; User Id=db14840; Password=************; Encrypt=False; MultipleActiveResultSets=True;"
  },
  "Redis": {
    "ConnectionString": "switchyard.proxy.rlwy.net:15694,password=OMLNKRNMjpCaWQXshlNitKtnoVsMxGhE,abortConnect=False",
    "InstanceName": "Croppilot_",
    "DefaultExpiration": "01:00:00",
    "LongTermExpiration": "24:00:00",
    "ShortTermExpiration": "00:15:00"
  },
  "AzureKey": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=graduationprojetct;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    // "StorageAccount": "elofatest2",
    // "ContainerName": "elofatest26c7fc8ad-9001-40bb-8031-6443179f47e2",
    "RoverStorge": "DefaultEndpointsProtocol=https;AccountName=roverstorge;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
  },
  "JwtSettings": {
    "Issuer": "CropGuard",
    "ValidateAudience": false,
    "ValidateIssuer": false,
    "ValidateIssuerSigningKey": true,
    "AccessTokenDurationInMinutes": 30,
    "RefreshTokenDurationInDays": 15,
    "Key": "CropGuard web 2025 project back-end hhh hhh hhhh",
    "Audience": "https://crop-guard.me"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Email": {
    "From": "<EMAIL>",
    "ApplicationName": "CropGuard",
    "ConfirmEmailPath": "confirm-email",
    "ResetPasswordPath": "reset-password"
  },
  "MailJet": {
    "ApiKey": "********************************",
    "SecretKey": "a857842597e744047328c84565a093c5"
  },
  "HealthCheck": {
    "EnableTestEmail": true,
    "TestEmail": "<EMAIL>",
    "TimeoutSeconds": 30
  },
  "Authentication": {
    "Google": {
      "ClientId": "806052617207-h9sqqe0q9ivl7g660deofptssgus6593.apps.googleusercontent.com",
      "ClientSecret": "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"
    },
    "Facebook": {
      "AppId": "1163766375364809",
      "AppSecret": "be37206c56544dbe47a065e644914777"
    }
  },
  "OpenAI": {
    "Endpoint": "https://20912-ma9xcgmn-eastus2.openai.azure.com/",
    "Key": "AGrmNxvIgI0ovJGDqahw7oTYBRzJwzK8WeEQ23t8nabQl3QXPlPXJQQJ99BEACHYHv6XJ3w3AAAAACOGlZbv",
    "DeploymentName": "gpt-4",
    "SystemMessage": "You are an agricultural expert with 10 years of experience in farming and crop management. You provide accurate and detailed advice on farming techniques, crop diseases, soil health, and sustainable practices."
  },
  "WatchDogSettings": {
    "WatchPageUsername": "admin",
    "WatchPagePassword": "123"
  },
  "HangfireSettings": {
    "DashboardPath": "/jobs",
    "Title": "Crop Guard Jobs Dashboard",
    "Username": "admin",
    "Password": "123"
  },
  "Stripe": {
    "Secretkey": "sk_test_51R0vQvFf1OfhUq8M9oUTNPfCmH8msg6excHD8yx0kMW838B2PZrH4esTsgnBrhboWRCjHLGfowPHJ0jRcdrURWNG00NspxCHYH",
    "PublishableKey": "pk_test_51R0vQvFf1OfhUq8MU9BhoKqumGe63TiaGC5SiV90EfkobJHDD9tx4A2rgNPvbRNOiOnrqKLWbRbdNA4iXv8RUHGf00uscoSe5D",
    "WebhookSecret": "whsec_5e5196de07a747839700b4d6109acfabcd21bdae7333217f5f86e8a356578438"
  },
  "Notification": {
    "Twilio": {
      "AccountSid": "**********************************",
      "AuthToken": "53bdcf55cd23bc9ca6f6d079efd53d8d",
      "PhoneNumber": "+***********",
      "MessagingServiceSid": "MGca1c2108d0ea44e733e81fc04c8e40fa"
    },
    "Pushbullet": {
      "PushbulletApiUrl": "https://api.pushbullet.com/v2/pushes",
      "AccessToken": "**********************************"
    }
  },
  "AzureService": {
    "CosmosDb": {
      "EndpointUri": "https://sensorground.documents.azure.com:443/",
      "PrimaryKey": "****************************************************************************************",
      "DatabaseId": "test",
      "ContainerId": "ehap-test"
    }
  },
  "AiModels": {
    "YoloModelPath": "AiModels/best.onnx",
    "MobileNetModelPath": "AiModels/mobilenet_model.onnx",
    "RLModelPath": "AiModels/Rl_Model.onnx"
  },
  "WeatherApi": {
    "ApiKey": "0b419fbe08ed6d587525b3cce2058de9"
  },
  "SeedUsers": {
    "Manager": {
      "Email": "<EMAIL>",
      "Password": "Mo123#"
    },
    "FrontAdmin": {
      "Email": "<EMAIL>",
      "Password": "Mo123#"
    },
    "FrontUser": {
      "Email": "<EMAIL>",
      "Password": "Mo123#"
    },
    "MobileAdmin": {
      "Email": "<EMAIL>",
      "Password": "Mo123#"
    },
    "MobileUser": {
      "Email": "<EMAIL>",
      "Password": "Mo123#"
    }
  }
}
