﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.Hangfire" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.Uris" Version="8.0.1" />
    <PackageReference Include="Hangfire.Dashboard.Basic.Authentication" Version="7.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.12" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.47.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.17" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="WatchDog.NET" Version="1.4.12" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10" />
    <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Croppilot.Core\Croppilot.Core.csproj" />
    <ProjectReference Include="..\Croppilot.Infrastructure\Croppilot.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
	  <Folder Include="images\" />
	  <None Include="AiModels\best.onnx">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </None>
	  <None Include="AiModels\mobilenet_model.onnx">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </None>
	  <None Include="AiModels\Rl_Model.onnx">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
	  </None>
  </ItemGroup>
</Project>
