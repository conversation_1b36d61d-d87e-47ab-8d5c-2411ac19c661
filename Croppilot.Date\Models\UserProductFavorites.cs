namespace Croppilot.Date.Models;

/// <summary>
/// Represents user-specific favorite status for products
/// </summary>
public record UserProductFavorites(
    string UserId,
    Dictionary<int, bool> FavoriteStatuses, // ProductId -> IsFavorite
    DateTime LastUpdated
);

/// <summary>
/// Represents a single product's favorite status for a user
/// </summary>
public record ProductFavoriteStatus(
    int ProductId,
    bool IsFavorite
); 